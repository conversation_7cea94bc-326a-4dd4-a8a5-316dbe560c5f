import { useQuery } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { userDashboardApi } from '../services/userDashboardApi';
import { transformUserDashboardToMetrics } from '../utils/transformUserMetrics';
import type { MetricCardProps } from '../components/shared/MetricCard';

export function useUserDashboard(period: string = 'month') {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', period],
    queryFn: () => userDashboardApi.getDashboardData(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}


export function useUserMetrics(period?: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', 'metrics', period],
    queryFn: () => userDashboardApi.getUserMetrics(period),
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserSalesTrend(period: string = '30d') {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', 'sales-trend', period],
    queryFn: () => userDashboardApi.getSalesTrend(period),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useUserSalesBreakdown(period: string = '30d', breakdownType: string = 'payment') {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', 'sales-breakdown', period, breakdownType],
    queryFn: () => userDashboardApi.getSalesBreakdown(period, breakdownType),
    staleTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useRecentSales(limit: number = 10) {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', 'recent-sales', limit],
    queryFn: () => userDashboardApi.getRecentSales(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useTargets() {
  return useQuery({
    queryKey: [QUERY_KEYS.DASHBOARD, 'user', 'targets'],
    queryFn: userDashboardApi.getTargets,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Custom hook that fetches user dashboard data and transforms it into MetricCardProps format
 */
export function useUserDashboardMetrics(period: string = 'month'): {
  data: MetricCardProps[] | undefined;
  isLoading: boolean;
  error: Error | null;
  isError: boolean;
} {
  const { data: dashboardData, isLoading, error, isError } = useUserDashboard(period);

  const transformedData = dashboardData
    ? transformUserDashboardToMetrics(dashboardData)
    : undefined;

  return {
    data: transformedData,
    isLoading,
    error,
    isError,
  };
}
