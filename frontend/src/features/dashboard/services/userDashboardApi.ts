import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  UserDashboardData,
  SalesBreakdownData,
  SalesTrendData,
  ApiResponse,
  DailyProgressMetrics,
} from '../types';

export const userDashboardApi = {
  getDashboardData: async (period: string = 'month'): Promise<UserDashboardData> => {
    const response = await apiClient.get<ApiResponse<UserDashboardData>>(
      API_ENDPOINTS.DASHBOARD.USER,
      {
        params: { period },
      },
    );
    return response.data.data;
  },

  getUserMetrics: async (period?: string): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.DASHBOARD.USER}/metrics`, {
      params: { period },
    });
    // Handle both wrapped and unwrapped responses for backward compatibility
    return response.data.data || response.data;
  },

  getSalesTrend: async (period: string = '30d'): Promise<SalesTrendData[]> => {
    const response = await apiClient.get<ApiResponse<SalesTrendData[]>>(
      `${API_ENDPOINTS.DASHBOARD.USER}/sales-trend`,
      {
        params: { period },
      },
    );
    return response.data.data;
  },

  getSalesBreakdown: async (
    period: string = '30d',
    breakdownType: string = 'payment',
  ): Promise<SalesBreakdownData[]> => {
    const response = await apiClient.get<ApiResponse<SalesBreakdownData[]>>(
      `${API_ENDPOINTS.DASHBOARD.USER}/sales-breakdown`,
      {
        params: { period, breakdownType },
      },
    );
    return response.data.data;
  },

  getRecentSales: async (limit: number = 10): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.DASHBOARD.USER}/recent-sales`, {
      params: { limit },
    });
    // Handle both wrapped and unwrapped responses for backward compatibility
    return response.data.data || response.data;
  },

  getTargets: async (): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.DASHBOARD.USER}/targets`);
    // Handle both wrapped and unwrapped responses for backward compatibility
    return response.data.data || response.data;
  },

  getDailyProgressMetrics: async (): Promise<DailyProgressMetrics> => {
    const response = await apiClient.get<ApiResponse<DailyProgressMetrics>>(
      `${API_ENDPOINTS.DASHBOARD.USER}/daily-progress`,
    );
    return response.data.data;
  },
};
