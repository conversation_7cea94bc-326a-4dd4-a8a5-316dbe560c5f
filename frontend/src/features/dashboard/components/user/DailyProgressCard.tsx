import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import { Separator } from '@/shared/components/ui/separator';
import { cn } from '@/lib/utils';
import { DailyProgressMetrics } from '../../types';
import { userDashboardApi } from '../../services/userDashboardApi';
import { Skeleton } from '@/shared/components/ui/skeleton';

const DailyProgressCard: React.FC = () => {
  const [isAdvancedView, setIsAdvancedView] = useState(false);

  const {
    data: dailyProgressData,
    isLoading,
    isError,
    error,
  } = useQuery<DailyProgressMetrics, Error>({
    queryKey: ['dailyProgressMetrics'],
    queryFn: userDashboardApi.getDailyProgressMetrics,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 3,
  });

  const toggleView = () => {
    setIsAdvancedView((prev) => !prev);
  };

  if (isLoading) {
    return (
      <Card className="flex flex-col col-span-1 animate-pulse">
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <Skeleton className="w-1/3 h-4" />
          <Skeleton className="w-24 h-8" />
        </CardHeader>
        <CardContent className="flex-grow p-4 overflow-y-auto">
          <Skeleton className="w-1/2 h-12 mx-auto mb-4" />
          <Skeleton className="w-3/4 h-4 mx-auto mb-2" />
          <Skeleton className="w-1/2 h-4 mx-auto" />
          <Skeleton className="w-full h-24 mt-6" />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className="flex flex-col col-span-1">
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">Daily Progress</CardTitle>
        </CardHeader>
        <CardContent className="flex-grow p-4 overflow-y-auto text-center text-destructive">
          <p>Error loading daily progress: {error?.message || 'Unknown error'}</p>
        </CardContent>
      </Card>
    );
  }

  if (!dailyProgressData) {
    return (
      <Card className="flex flex-col col-span-1">
        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle className="text-sm font-medium">Daily Progress</CardTitle>
        </CardHeader>
        <CardContent className="flex-grow p-4 overflow-y-auto text-center text-muted-foreground">
          <p>No daily progress data available.</p>
        </CardContent>
      </Card>
    );
  }

  // Simple View
  const SimpleView: React.FC<{ data: DailyProgressMetrics }> = ({ data }) => (
    <div className="flex flex-col items-center justify-center p-4">
      <div className="text-5xl font-bold text-primary">{data.completionPercentage}%</div>
      <Progress value={data.completionPercentage} className="w-3/4 mt-4" />
      <p className="mt-2 text-sm text-muted-foreground">Daily Goal Completion</p>
      <div className="mt-6 text-center">
        <h4 className="text-lg font-semibold">Goals Today:</h4>
        <ul className="mt-2 text-left list-disc list-inside">
          {data.dailyGoals.map((goal: string, index: number) => (
            <li key={index}>{goal}</li>
          ))}
        </ul>
        <p className="mt-4 text-sm text-muted-foreground">
          Remaining: {data.remainingGoals.length} goals
        </p>
      </div>
    </div>
  );

  // Advanced View
  const AdvancedView: React.FC<{ data: DailyProgressMetrics }> = ({ data }) => (
    <div className="p-4">
      <h3 className="mb-4 text-xl font-bold">Detailed Daily Progress</h3>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <p className="text-sm text-muted-foreground">Completion Percentage:</p>
          <p className="text-2xl font-bold">{data.completionPercentage}%</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Total Sales Today:</p>
          <p className="text-2xl font-bold">${data.totalSalesToday.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Items Sold Today:</p>
          <p className="text-2xl font-bold">{data.itemsSoldToday}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Average Sale Value Today:</p>
          <p className="text-2xl font-bold">${data.averageSaleValueToday.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Sales Target Today:</p>
          <p className="text-2xl font-bold">${data.salesTargetToday.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Target Achievement Today:</p>
          <p className="text-2xl font-bold">{data.targetAchievementToday}%</p>
        </div>
      </div>
      <Separator className="my-6" />
      <h4 className="mb-2 text-lg font-semibold">Daily Goals:</h4>
      <ul className="text-left list-disc list-inside">
        {data.dailyGoals.map((goal: string, index: number) => (
          <li key={index}>{goal}</li>
        ))}
      </ul>
      <h4 className="mt-4 mb-2 text-lg font-semibold">Remaining Goals:</h4>
      <ul className="text-left list-disc list-inside">
        {data.remainingGoals.map((goal: string, index: number) => (
          <li key={index}>{goal}</li>
        ))}
      </ul>
    </div>
  );

  return (
    <Card className="flex flex-col col-span-1">
      <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
        <CardTitle className="text-sm font-medium">Daily Progress</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleView}
          aria-expanded={isAdvancedView}
          aria-controls="daily-progress-content"
        >
          {isAdvancedView ? 'Simple View' : 'Advanced View'}
        </Button>
      </CardHeader>
      <CardContent id="daily-progress-content" className="flex-grow overflow-y-auto">
        {isAdvancedView ? <AdvancedView data={dailyProgressData} /> : <SimpleView data={dailyProgressData} />}
      </CardContent>
    </Card>
  );
};

export default DailyProgressCard;