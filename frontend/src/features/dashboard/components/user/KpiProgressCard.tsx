import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>eader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';

export interface CurrentSalesFigures {
  progress: number;
  total: number;
}
export interface KpiProgressCardProps {
  selectedPeriod: string;
  currentSalesFigures: CurrentSalesFigures;
  currentKpiTarget: number;
}

// Helper function to determine color classes based on progress
const getProgressColorClasses = (progress: number) => {
  let bgColorClass = '';
  let textColorClass = '';

  // Red: 0-20%
  if (progress <= 20) {
    bgColorClass = 'bg-red-500';
    textColorClass = 'text-red-500';
  }
  // Orange: 21-40%
  else if (progress <= 40) {
    bgColorClass = 'bg-orange-500';
    textColorClass = 'text-orange-500';
  }
  // Yellow: 41-60%
  else if (progress <= 60) {
    bgColorClass = 'bg-yellow-500';
    textColorClass = 'text-yellow-500';
  }
  // Lime: 61-80%
  else if (progress <= 80) {
    bgColorClass = 'bg-lime-500';
    textColorClass = 'text-lime-500';
  }
  // Green: 81-100%
  else {
    bgColorClass = 'bg-green-500';
    textColorClass = 'text-green-500';
  }
  return { bgColorClass, textColorClass };
};

export function KpiProgressCard({
  selectedPeriod,
  currentSalesFigures,
  currentKpiTarget,
}: KpiProgressCardProps) {
  const { progress } = currentSalesFigures;
  const { bgColorClass, textColorClass } = getProgressColorClasses(progress);

  return (
    <Card>
      <CardHeader>
        <CardTitle>KPI Progress ({selectedPeriod})</CardTitle>
        <CardDescription>Your sales progress towards the current target.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-4 mb-2">
          <Progress
            value={progress}
            aria-label="outlet performance progress"
            className={`transition-colors duration-500 ${bgColorClass}`}
          />
          <span className={`text-base font-medium transition-colors duration-500 ${textColorClass}`}>
            {Math.round(progress)}%
          </span>
        </div>
        <p className="text-sm text-muted-foreground">
          Achieved: RM{' '}
          {(currentSalesFigures.total || 0).toLocaleString('en-MY', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}{' '}
          / Target: RM{' '}
          {(currentKpiTarget || 0).toLocaleString('en-MY', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </p>
      </CardContent>
    </Card>
  );
}
