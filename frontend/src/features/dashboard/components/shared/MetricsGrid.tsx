import { MetricCard, MetricCardProps } from './MetricCard';
import DailyProgressCard from '../user/DailyProgressCard';
import { KpiProgressCard } from '../user/KpiProgressCard';

interface MetricsGridProps {
  metrics: MetricCardProps[];
  cols?: 1 | 2 | 3 | 4;
  selectedPeriod: string;
  currentSalesFigures: { progress: number; total: number };
  currentKpiTarget: number;
}

export const MetricsGrid = ({
  metrics,
  cols = 4,
  selectedPeriod,
  currentSalesFigures,
  currentKpiTarget,
}: MetricsGridProps) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={`mb-8 grid gap-4 ${gridClasses[cols]}`}>
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
      <DailyProgressCard />
      <KpiProgressCard
        selectedPeriod={selectedPeriod}
        currentSalesFigures={currentSalesFigures}
        currentKpiTarget={currentKpiTarget}
      />
    </div>
  );
};
