import { useState } from 'react';
import { MetricsGrid } from '../components/shared/MetricsGrid';
import {
  DashboardErrorBoundary,
  DashboardLoadingSkeleton,
  QueryErrorDisplay,
} from '../components/shared/DashboardErrorBoundary';
import { SalesBreakdownChartCard } from '../components/shared/SalesBreakdownChartCard';
import {
  useUserDashboard,
  useUserSalesTrend,
  useUserSalesBreakdown,
  useUserDashboardMetrics,
} from '../hooks/useUserDashboard';
import { useChartDimensions } from '../hooks/useChartDimensions';
import { SalesTrendChartCard } from '../components/shared/SalesTrendChartCard';
import { KpiProgressCard } from '../components/user/KpiProgressCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';

export function UserDashboardPage() {
  const { pieInnerRadius, pieOuterRadius } = useChartDimensions();
  const [dashboardPeriod, setDashboardPeriod] = useState('month');
  const [salesTrendView, setSalesTrendView] = useState('30d');
  const [breakdownView, setBreakdownView] = useState('payment');

  const { data: dashboardData, isLoading, error } = useUserDashboard(dashboardPeriod);
  const {
    data: salesTrendData,
    isLoading: salesTrendLoading,
    error: salesTrendError,
  } = useUserSalesTrend(salesTrendView);
  const {
    data: salesBreakdownData,
    isLoading: salesBreakdownLoading,
    error: salesBreakdownError,
  } = useUserSalesBreakdown(salesTrendView, breakdownView);
  const {
    data: metricsData,
    isLoading: metricsLoading,
    error: metricsError,
  } = useUserDashboardMetrics(dashboardPeriod);

  if (
    isLoading ||
    metricsLoading ||
    salesTrendLoading ||
    salesBreakdownLoading
  ) {
    return <DashboardLoadingSkeleton />;
  }

  if (error || metricsError || salesTrendError || salesBreakdownError) {
    const errorMessage =
      error?.message ||
      metricsError?.message ||
      salesTrendError?.message ||
      salesBreakdownError?.message;
    const isValidationError =
      errorMessage?.includes('Invalid query parameters') ||
      errorMessage?.includes('Invalid period');

    return (
      <QueryErrorDisplay
        error={
          error || metricsError || salesTrendError || salesBreakdownError
        }
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <DashboardErrorBoundary>
      <div className="space-y-8">
        {/* Header Section */}
        <header className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">My Dashboard</h1>
              <p className="text-lg text-muted-foreground">
                Track your sales performance and progress towards your goals.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Period:</span>
              <Select value={dashboardPeriod} onValueChange={setDashboardPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                  <SelectItem value="month">Month</SelectItem>
                  <SelectItem value="quarter">Quarter</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </header>

        {/* Primary KPI Section */}
        <section className="space-y-6">
          <div>
            <h2 className="mb-4 text-xl font-semibold">Performance Overview</h2>
            <div className="grid gap-6 md:grid-cols-2">
            </div>
          </div>
        </section>

        {/* Metrics Overview Section */}
        <section className="space-y-6">
          <div>
            <h2 className="mb-4 text-xl font-semibold">Key Metrics</h2>
            <MetricsGrid
              metrics={metricsData || []}
              selectedPeriod={dashboardPeriod}
              currentSalesFigures={{
                progress: dashboardData?.targetAchievement || 0,
                total: dashboardData?.monthlySales || 0,
              }}
              currentKpiTarget={dashboardData?.salesTarget || 0}
            />
          </div>
        </section>

        {/* Charts Section */}
        <section className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <SalesTrendChartCard
                salesTrendView={salesTrendView}
                onSalesTrendViewChange={setSalesTrendView}
                displayedSalesTrendData={salesTrendData || []}
                trendOptions={['7d', '30d', '90d', '1y']}
              />
            </div>
            <div className="lg:col-span-1">
              <SalesBreakdownChartCard
                breakdownView={breakdownView}
                onBreakdownViewChange={setBreakdownView}
                displayedSalesBreakdownData={salesBreakdownData || []}
                breakdownOptions={['payment', 'time', 'location']}
                pieInnerRadius={pieInnerRadius}
                pieOuterRadius={pieOuterRadius}
              />
            </div>
          </div>
        </section>
      </div>
    </DashboardErrorBoundary>
  );
}
