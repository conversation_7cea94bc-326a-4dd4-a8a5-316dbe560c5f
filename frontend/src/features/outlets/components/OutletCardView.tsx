import { useMemo, useState } from 'react';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  User,
  Activity,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Card, CardContent, CardHeader } from '@/shared/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { OutletWithManager, OutletFilters } from '../types';
import { OutletAdvancedFilters } from './OutletAdvancedFilters';

interface OutletCardViewProps {
  outlets: OutletWithManager[];
  loading?: boolean;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
  onView?: (outlet: OutletWithManager) => void;
  onBulkDelete?: (outlets: OutletWithManager[]) => void;
  onFiltersChange?: (filters: OutletFilters) => void;
  filters?: OutletFilters;
  pagination?: {
    pageIndex: number;
    pageSize: number;
    totalPages: number;
    totalItems: number;
  };
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void;
  className?: string;
}

interface OutletCardProps {
  outlet: OutletWithManager;
  isSelected: boolean;
  onSelect: (selected: boolean) => void;
  onEdit?: (outlet: OutletWithManager) => void;
  onDelete?: (outlet: OutletWithManager) => void;
  onView?: (outlet: OutletWithManager) => void;
}

function OutletCard({ outlet, isSelected, onSelect, onEdit, onDelete, onView }: OutletCardProps) {
  return (
    <Card
      className={cn(
        'transition-all duration-200 hover:shadow-md',
        isSelected && 'ring-primary ring-2 ring-offset-2'
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex min-w-0 flex-1 items-start gap-3">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
              className="mt-1 flex-shrink-0"
              aria-label={`Select outlet ${outlet.name}`}
            />

            <div className="min-w-0 flex-1">
              <div className="mb-2 flex items-center gap-2">
                <Building2 className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                <h3 className="truncate text-sm font-medium">{outlet.name}</h3>
                <Badge
                  variant={outlet.isActive ? 'default' : 'secondary'}
                  className="flex-shrink-0 gap-1"
                >
                  <Activity className="h-3 w-3" />
                  {outlet.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              {outlet.address && (
                <div className="text-muted-foreground mb-2 flex items-start gap-2 text-sm">
                  <MapPin className="mt-0.5 h-3 w-3 flex-shrink-0" />
                  <span className="line-clamp-2">{outlet.address}</span>
                </div>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 flex-shrink-0 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {onView && (
                <DropdownMenuItem onClick={() => onView(outlet)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(outlet)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Outlet
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem onClick={() => onDelete(outlet)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Outlet
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Contact Information */}
          <div className="space-y-2">
            {(outlet.phone || outlet.phoneNumber) && (
              <div className="flex items-center gap-2 text-sm">
                <Phone className="text-muted-foreground h-3 w-3" />
                <span>{outlet.phone || outlet.phoneNumber}</span>
              </div>
            )}
            {outlet.email && (
              <div className="flex items-center gap-2 text-sm">
                <Mail className="text-muted-foreground h-3 w-3" />
                <span className="truncate">{outlet.email}</span>
              </div>
            )}
          </div>

          {/* Manager Information */}
          {outlet.manager ? (
            <div className="flex items-center gap-2 text-sm">
              <User className="text-muted-foreground h-3 w-3" />
              <span>{outlet.manager.fullName}</span>
            </div>
          ) : (
            <div className="text-muted-foreground flex items-center gap-2 text-sm">
              <User className="h-3 w-3" />
              <span>No manager assigned</span>
            </div>
          )}

          {/* Created Date */}
          <div className="text-muted-foreground text-xs">
            Created: {new Date(outlet.createdAt).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function OutletCardView({
  outlets,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onBulkDelete,
  onFiltersChange,
  filters = {},
  pagination,
  onPaginationChange,
  className,
}: OutletCardViewProps) {
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // Get selected outlets
  const selectedOutlets = useMemo(() => {
    return outlets.filter((_, index) => rowSelection[index]);
  }, [outlets, rowSelection]);

  // Handle individual outlet selection
  const handleOutletSelect = (index: number, selected: boolean) => {
    setRowSelection(prev => ({
      ...prev,
      [index]: selected,
    }));
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      const newSelection: Record<string, boolean> = {};
      outlets.forEach((_, index) => {
        newSelection[index] = true;
      });
      setRowSelection(newSelection);
    } else {
      setRowSelection({});
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedOutlets.length > 0 && onBulkDelete) {
      onBulkDelete(selectedOutlets);
      setRowSelection({});
    }
  };

  const isAllSelected = outlets.length > 0 && selectedOutlets.length === outlets.length;
  const isPartiallySelected = selectedOutlets.length > 0 && selectedOutlets.length < outlets.length;

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold">Outlets</h3>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={isAllSelected}
                ref={el => {
                  if (el) el.indeterminate = isPartiallySelected;
                }}
                onCheckedChange={handleSelectAll}
                aria-label="Select all outlets"
              />
              <span className="text-muted-foreground text-sm">
                {selectedOutlets.length > 0 ? `${selectedOutlets.length} selected` : 'Select all'}
              </span>
            </div>
          </div>

          {selectedOutlets.length > 0 && onBulkDelete && (
            <Button variant="destructive" size="sm" onClick={handleBulkDelete} className="gap-2">
              <Trash2 className="h-4 w-4" />
              Delete Selected ({selectedOutlets.length})
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        <OutletAdvancedFilters filters={filters} onFiltersChange={onFiltersChange || (() => {})} />
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader className="pb-3">
                  <div className="space-y-2">
                    <div className="bg-muted h-4 w-3/4 rounded"></div>
                    <div className="bg-muted h-3 w-1/2 rounded"></div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="bg-muted h-3 w-full rounded"></div>
                    <div className="bg-muted h-3 w-2/3 rounded"></div>
                    <div className="bg-muted h-3 w-1/2 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : outlets.length === 0 ? (
          <div className="py-12 text-center">
            <Building2 className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-medium">No outlets found</h3>
            <p className="text-muted-foreground">
              {Object.keys(filters).length > 0
                ? 'No outlets match your current filters. Try adjusting your search criteria.'
                : 'No outlets have been created yet.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {outlets.map((outlet, index) => (
              <OutletCard
                key={outlet.id}
                outlet={outlet}
                isSelected={!!rowSelection[index]}
                onSelect={selected => handleOutletSelect(index, selected)}
                onEdit={onEdit}
                onDelete={onDelete}
                onView={onView}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination && onPaginationChange && outlets.length > 0 && (
          <div className="flex items-center justify-between border-t pt-6">
            <div className="text-muted-foreground text-sm">
              Showing {pagination.pageIndex * pagination.pageSize + 1} to{' '}
              {Math.min((pagination.pageIndex + 1) * pagination.pageSize, pagination.totalItems)} of{' '}
              {pagination.totalItems} outlets
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex - 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex === 0}
              >
                Previous
              </Button>
              <div className="flex items-center gap-1">
                <span className="text-sm">
                  Page {pagination.pageIndex + 1} of {pagination.totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  onPaginationChange({
                    pageIndex: pagination.pageIndex + 1,
                    pageSize: pagination.pageSize,
                  })
                }
                disabled={pagination.pageIndex >= pagination.totalPages - 1}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
