import { Table, LayoutGrid } from 'lucide-react';
import { ToggleGroup, ToggleGroupItem } from '@/shared/components/ui/toggle-group';
import { cn } from '@/lib/utils';

export type ViewMode = 'table' | 'card';

interface ViewToggleProps {
  value: ViewMode;
  onValueChange: (value: ViewMode) => void;
  className?: string;
}

export function ViewToggle({ value, onValueChange, className }: ViewToggleProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-muted-foreground hidden text-sm font-medium sm:inline">View:</span>
      <ToggleGroup
        type="single"
        value={value}
        onValueChange={newValue => {
          if (newValue) {
            onValueChange(newValue as ViewMode);
          }
        }}
        className={cn('rounded-md border', className)}
        variant="outline"
        aria-label="Choose view mode"
      >
        <ToggleGroupItem value="table" aria-label="Table view" className="gap-2">
          <Table className="h-4 w-4" />
          <span className="hidden sm:inline">Table</span>
        </ToggleGroupItem>
        <ToggleGroupItem value="card" aria-label="Card view" className="gap-2">
          <LayoutGrid className="h-4 w-4" />
          <span className="hidden sm:inline">Cards</span>
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
}
