import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { env } from '@/lib/config/env';
import { API_CONFIG } from '@/lib/config/constants';
import { getStoredToken, removeStoredToken } from '@/lib/storage/localStorage';
import { errorReporting } from '@/lib/monitoring/errorReporting';

// Create axios instance
// Log initial configuration in development mode
if (import.meta.env.MODE === 'development') {
  console.log('API Client Configuration:', {
    baseURL: env.API_BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
  });
}

const apiClient: AxiosInstance = axios.create({
  baseURL: env.API_BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  config => {
    console.log('Making API request:', {
      method: config.method,
      url: config.url,
      baseURL: config.baseURL,
      headers: config.headers,
    });
    const token = getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Only log in development mode
    if (import.meta.env.MODE === 'development') {
      console.log('API Response:', {
        status: response.status,
        url: response.config.url,
        method: response.config.method,
      });
    }
    return response;
  },
  error => {
    // Report error to monitoring service
    const apiDetails = {
      endpoint: error.config?.url || 'unknown',
      method: error.config?.method?.toUpperCase() || 'unknown',
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      requestData: error.config?.data,
      headers: error.config?.headers,
    };

    // Determine error type and report accordingly
    if (error.response?.status === 401) {
      errorReporting.reportAuthError(error, { url: window.location.href });
    } else if (!error.response) {
      errorReporting.reportNetworkError(error, { url: window.location.href });
    } else {
      errorReporting.reportApiError(error, apiDetails, { url: window.location.href });
    }

    // Log errors in development mode
    if (import.meta.env.MODE === 'development') {
      console.error('API Error:', {
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method,
        message: error.message,
        data: error.response?.data,
      });
    }

    // Handle 401 errors by removing token and redirecting to login
    if (error.response?.status === 401) {
      if (import.meta.env.MODE === 'development') {
        console.warn('Authentication failed - removing token and redirecting to login');
      }
      removeStoredToken();

      // Only redirect if not already on login page and not an auth endpoint
      const isAuthEndpoint = error.config?.url?.includes('/auth/');
      const isLoginPage = window.location.pathname.includes('/login');

      if (!isLoginPage && !isAuthEndpoint) {
        // Add a small delay to allow React Query to handle the error first
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      }
    }

    // Handle 403 errors (forbidden)
    if (error.response?.status === 403) {
      if (import.meta.env.MODE === 'development') {
        console.warn('Access forbidden - insufficient permissions');
      }
      error.message = 'You do not have permission to access this resource.';
    }

    // Handle network errors
    if (!error.response) {
      if (import.meta.env.MODE === 'development') {
        console.error('Network Error Details:', {
          message: error.message,
          config: error.config,
          code: error.code,
          stack: error.stack,
        });
      }
      error.message = 'Network error. Please check your connection.';
    }

    return Promise.reject(error);
  }
);

export { apiClient };

// Helper functions for common HTTP methods
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),

  post: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> => apiClient.post(url, data, config),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),

  patch: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> => apiClient.patch(url, data, config),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),
};
