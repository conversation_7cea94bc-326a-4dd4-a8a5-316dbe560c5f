/**
 * Error reporting and monitoring utilities
 * Provides centralized error logging and monitoring for production applications
 */

export interface ErrorContext {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  sessionId?: string;
  buildVersion?: string;
}

export interface ApiErrorDetails {
  endpoint: string;
  method: string;
  status?: number;
  statusText?: string;
  responseData?: any;
  requestData?: any;
  headers?: Record<string, string>;
}

export interface ErrorReport {
  id: string;
  type: 'api_error' | 'auth_error' | 'network_error' | 'runtime_error' | 'validation_error';
  message: string;
  stack?: string;
  context: ErrorContext;
  apiDetails?: ApiErrorDetails;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
  createdAt: string;
}

class ErrorReportingService {
  private errors: ErrorReport[] = [];
  private maxErrors = 100; // Keep last 100 errors in memory
  private isProduction = import.meta.env.MODE === 'production';

  /**
   * Report an API error
   */
  reportApiError(error: any, apiDetails: ApiErrorDetails, context: Partial<ErrorContext> = {}) {
    const errorReport: ErrorReport = {
      id: this.generateId(),
      type: 'api_error',
      message: error.message || 'API request failed',
      stack: error.stack,
      context: this.enrichContext(context),
      apiDetails,
      severity: this.determineSeverity(error, 'api_error'),
      resolved: false,
      createdAt: new Date().toISOString(),
    };

    this.addError(errorReport);
    this.logError(errorReport);
  }

  /**
   * Report an authentication error
   */
  reportAuthError(error: any, context: Partial<ErrorContext> = {}) {
    const errorReport: ErrorReport = {
      id: this.generateId(),
      type: 'auth_error',
      message: error.message || 'Authentication failed',
      stack: error.stack,
      context: this.enrichContext(context),
      severity: 'high',
      resolved: false,
      createdAt: new Date().toISOString(),
    };

    this.addError(errorReport);
    this.logError(errorReport);
  }

  /**
   * Report a network error
   */
  reportNetworkError(error: any, context: Partial<ErrorContext> = {}) {
    const errorReport: ErrorReport = {
      id: this.generateId(),
      type: 'network_error',
      message: error.message || 'Network request failed',
      stack: error.stack,
      context: this.enrichContext(context),
      severity: 'medium',
      resolved: false,
      createdAt: new Date().toISOString(),
    };

    this.addError(errorReport);
    this.logError(errorReport);
  }

  /**
   * Report a runtime error
   */
  reportRuntimeError(error: any, context: Partial<ErrorContext> = {}) {
    const errorReport: ErrorReport = {
      id: this.generateId(),
      type: 'runtime_error',
      message: error.message || 'Runtime error occurred',
      stack: error.stack,
      context: this.enrichContext(context),
      severity: this.determineSeverity(error, 'runtime_error'),
      resolved: false,
      createdAt: new Date().toISOString(),
    };

    this.addError(errorReport);
    this.logError(errorReport);
  }

  /**
   * Get all error reports
   */
  getErrors(): ErrorReport[] {
    return [...this.errors];
  }

  /**
   * Get errors by type
   */
  getErrorsByType(type: ErrorReport['type']): ErrorReport[] {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * Get unresolved errors
   */
  getUnresolvedErrors(): ErrorReport[] {
    return this.errors.filter(error => !error.resolved);
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string) {
    const error = this.errors.find(e => e.id === errorId);
    if (error) {
      error.resolved = true;
    }
  }

  /**
   * Clear all errors
   */
  clearErrors() {
    this.errors = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const total = this.errors.length;
    const unresolved = this.getUnresolvedErrors().length;
    const byType = this.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const bySeverity = this.errors.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      unresolved,
      byType,
      bySeverity,
    };
  }

  private addError(error: ErrorReport) {
    this.errors.unshift(error);
    
    // Keep only the last maxErrors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }
  }

  private logError(error: ErrorReport) {
    if (!this.isProduction) {
      console.group(`🚨 ${error.type.toUpperCase()}: ${error.message}`);
      console.error('Error Details:', error);
      if (error.stack) {
        console.error('Stack Trace:', error.stack);
      }
      if (error.apiDetails) {
        console.error('API Details:', error.apiDetails);
      }
      console.error('Context:', error.context);
      console.groupEnd();
    }

    // In production, you would send this to your monitoring service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    if (this.isProduction) {
      // this.sendToMonitoringService(error);
    }
  }

  private enrichContext(context: Partial<ErrorContext>): ErrorContext {
    return {
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      buildVersion: import.meta.env.VITE_REACT_APP_VERSION || 'unknown',
      ...context,
    };
  }

  private determineSeverity(error: any, type: ErrorReport['type']): ErrorReport['severity'] {
    // Authentication errors are always high severity
    if (type === 'auth_error') return 'high';
    
    // API errors based on status code
    if (type === 'api_error') {
      const status = error.response?.status;
      if (status >= 500) return 'critical';
      if (status >= 400) return 'medium';
      return 'low';
    }

    // Network errors are medium severity
    if (type === 'network_error') return 'medium';

    // Runtime errors based on error type
    if (type === 'runtime_error') {
      if (error.name === 'ChunkLoadError') return 'medium';
      if (error.message?.includes('Script error')) return 'low';
      return 'high';
    }

    return 'medium';
  }

  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('session_id', sessionId);
    }
    return sessionId;
  }
}

// Export singleton instance
export const errorReporting = new ErrorReportingService();

// Global error handler for unhandled errors
window.addEventListener('error', (event) => {
  errorReporting.reportRuntimeError(event.error || new Error(event.message), {
    url: event.filename,
  });
});

// Global handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  errorReporting.reportRuntimeError(event.reason, {
    url: window.location.href,
  });
});
